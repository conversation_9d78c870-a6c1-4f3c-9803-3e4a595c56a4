import { useState } from "react";
import { roles, userDataStore, UserToken } from "../data";
import { Store } from "react-data-stores";
import { getProfileRouteForRole, getProfileIconForRole } from "../utils/profileRouting";

const SectionsByRole = {
  [roles.doctor]: [
    {
      name: "Dashboard",
      icon: <i className="fa-solid fa-gauge"></i>,
      props: {
        onClick: () => Store.navigateTo("/doctor/dashboard"),
        style: { cursor: "pointer" },
      },
    },
    {
      name: "Patients Folders",
      icon: <i className="fa-solid fa-folder"></i>,
      props: {
        onClick: () => Store.navigateTo("/doctor/visits"),
        style: { cursor: "pointer" },
      },
    },
    {
      name: "Appointements",
      icon: <i className="fa-solid fa-calendar-alt"></i>,
      props: {
        onClick: () => Store.navigateTo("/doctor"),
        style: { cursor: "pointer" },
      },
    },
  ],
  [roles.facilityManager]: [
    {
      name: "Dashboard",
      icon: <i className="fa-solid fa-gauge"></i>,
      props: {
        onClick: () => Store.navigateTo("/facility/dashboard"),
        style: { cursor: "pointer" },
      },
    },
    {
      name: "Workers",
      icon: <i className="fa-solid fa-folder"></i>,
      props: {
        onClick: () => Store.navigateTo("/facility/workers"),
        style: { cursor: "pointer" },
      },
    },
  ],
};

export default function SideBar() {
  const [sideBarExpanded, setSideBarExpantion] = useState(false);
  const [selectedMenuItem, setSelectedMenuItem] = useState("");
  const [userData, setUserData] = userDataStore.useStore();
  if (!userData.token) return null;
  const Sections = SectionsByRole[userData.data.role];
  return (
    <div
      className={`fixed top-0  h-screen z-1000 transition-left duration-300 ease-in-out ${
        sideBarExpanded ? "left-0" : "left-[-250px]"
      }`}
    >
      <span
        className="absolute top-4 right-[-2.5rem] w-8 h-8 bg-[var(--secondary-color)] text-white rounded-full flex items-center justify-center cursor-pointer shadow-md"
        onClick={() => setSideBarExpantion((prev) => !prev)}
      >
        <i className="fa-solid fa-arrow-right"></i>
      </span>

      <div className="w-[250px] h-full flex flex-col bg-[var(--secondary-color)] text-white shadow-md">
        <div className="flex-1 overflow-y-auto py-4">
          <div className="flex items-center px-4 pb-4 mb-4 border-b border-white/10">
            <div className="w-10 h-10 bg-[var(--primary-color)] rounded-full flex items-center justify-center mr-3 text-lg">
              <i className="fa-solid fa-house-chimney-medical"></i>
            </div>
            <span className="font-bold text-lg">MediCare</span>
          </div>

          <div className="flex flex-col gap-2 px-2">
            {Sections?.map((section) => (
              <div
                key={section.name}
                className={`flex flex-col ${
                  selectedMenuItem === section.name ? "bg-white/10" : ""
                }`}
              >
                {section.items ? (
                  <>
                    <div
                      className="flex items-center justify-between px-4 py-3 cursor-pointer hover:bg-white/5"
                      onClick={() =>
                        setSelectedMenuItem((prev) =>
                          prev !== section.name ? section.name : ""
                        )
                      }
                    >
                      <div className="flex items-center gap-3">
                        <span className="w-5 text-center">{section.icon}</span>
                        <span>{section.name}</span>
                      </div>
                      <i
                        className={`fa-solid fa-caret-${
                          selectedMenuItem === section.name ? "up" : "down"
                        }`}
                      />
                    </div>

                    <div
                      className={`flex flex-col overflow-hidden transition-[max-height] duration-300 ease-in-out px-4 ${
                        selectedMenuItem === section.name
                          ? "max-h-[500px]"
                          : "max-h-0"
                      }`}
                    >
                      {section.items.map((item) => (
                        <div
                          key={item.name}
                          className="flex items-center gap-3 py-2 pl-6 pr-4 cursor-pointer hover:bg-white/5"
                          {...item.props}
                        >
                          <span className="w-5 text-center">{item.icon}</span>
                          <span>{item.name}</span>
                        </div>
                      ))}
                    </div>
                  </>
                ) : (
                  <div
                    className="flex items-center gap-3 px-4 py-3 cursor-pointer hover:bg-white/5"
                    {...section.props}
                    onClick={(e) => {
                      section.props.onClick(e);
                      setSideBarExpantion((prev) => !prev);
                    }}
                  >
                    <span className="w-5 text-center">{section.icon}</span>
                    <span>{section.name}</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="flex items-center justify-between px-4 py-3 border-t border-white/10 bg-black/20">
          <div
            className="flex items-center gap-3 cursor-pointer hover:bg-white/5 rounded-lg p-2 transition-colors"
            onClick={() => {
              Store.navigateTo(getProfileRouteForRole(userData.data.role));
            }}
          >
            <div className="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center">
              {getProfileIconForRole(userData.data.role)}
            </div>
            <span className="text-sm truncate max-w-[120px]">
              {userData.data.name?.join(" ") || "User"}
            </span>
          </div>
          <button
            className="w-8 h-8 flex items-center justify-center rounded-full text-white hover:bg-white/10 transition"
            onClick={() => {
              localStorage.removeItem(UserToken);
              window.location.reload();            }}
          >
            <i className="fa-solid fa-right-from-bracket"></i>
          </button>
        </div>
      </div>
    </div>
  );
}
